import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { increaseCredits } from "@/services/credit";
import { createOrder } from "@/services/order";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { code: 401, message: "请先登录" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { plan_id, credits, price, currency, interval, valid_months } = body;

    // 验证必要参数
    if (!plan_id || !credits || !price || !currency) {
      return NextResponse.json(
        { code: 400, message: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 创建订单记录
    const order = await createOrder({
      user_email: session.user.email,
      plan_id,
      credits,
      price,
      currency,
      interval: interval || 'one-time',
      valid_months: valid_months || 0,
      status: 'pending'
    });

    // 这里应该集成PayPal支付
    // 暂时模拟支付成功，直接添加积分
    if (process.env.NODE_ENV === 'development') {
      // 开发环境直接添加积分
      await increaseCredits(session.user.email, credits);
      
      return NextResponse.json({
        code: 0,
        message: "订阅成功",
        data: {
          order_id: order.id,
          credits_added: credits,
          // 在生产环境中，这里应该返回PayPal支付链接
          payment_url: null
        }
      });
    }

    // 生产环境应该返回PayPal支付链接
    return NextResponse.json({
      code: 0,
      message: "订单创建成功，请完成支付",
      data: {
        order_id: order.id,
        // TODO: 集成PayPal后返回实际的支付链接
        payment_url: "https://paypal.com/checkout/..." 
      }
    });

  } catch (error) {
    console.error("Subscribe error:", error);
    return NextResponse.json(
      { code: 500, message: "服务器错误" },
      { status: 500 }
    );
  }
}

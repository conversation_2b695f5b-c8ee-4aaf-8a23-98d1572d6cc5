"use client";

import { useEffect, useState } from "react";
import { Users, ShoppingCart, CreditCard, TrendingUp } from "lucide-react";

interface AdminStats {
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  recentUsers: any[];
  recentOrders: any[];
}

export default function AdminPage() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // 获取用户统计
        const usersResponse = await fetch('/api/admin/users', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ page: 1, limit: 5 }),
        });

        // 获取订单统计
        const ordersResponse = await fetch('/api/admin/orders', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ page: 1, limit: 5 }),
        });

        if (usersResponse.ok && ordersResponse.ok) {
          const usersData = await usersResponse.json();
          const ordersData = await ordersResponse.json();

          if (usersData.code === 0 && ordersData.code === 0) {
            // 计算总收入
            const totalRevenue = ordersData.data.orders.reduce((sum: number, order: any) => {
              return sum + (order.status === 'paid' ? order.amount / 100 : 0);
            }, 0);

            setStats({
              totalUsers: usersData.data.total,
              totalOrders: ordersData.data.total,
              totalRevenue,
              recentUsers: usersData.data.users,
              recentOrders: ordersData.data.orders,
            });
          }
        }
      } catch (error) {
        console.error('Failed to fetch admin stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总用户数</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalUsers || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <ShoppingCart className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总订单数</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.totalOrders || 0}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <CreditCard className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总收入</p>
              <p className="text-2xl font-bold text-gray-900">${stats?.totalRevenue?.toFixed(2) || '0.00'}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">转化率</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.totalUsers && stats?.totalOrders 
                  ? ((stats.totalOrders / stats.totalUsers) * 100).toFixed(1) + '%'
                  : '0%'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 快速导航 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">管理功能</h3>
          <div className="space-y-3">
            <a
              href="/admin/users"
              className="block p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center">
                <Users className="w-5 h-5 text-gray-600 mr-3" />
                <span className="font-medium">用户管理</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">查看和管理所有注册用户</p>
            </a>
            
            <a
              href="/admin/orders"
              className="block p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center">
                <ShoppingCart className="w-5 h-5 text-gray-600 mr-3" />
                <span className="font-medium">订单管理</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">查看和管理所有订单记录</p>
            </a>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">最近活动</h3>
          <div className="space-y-3">
            {stats?.recentUsers?.slice(0, 3).map((user, index) => (
              <div key={index} className="flex items-center p-2 bg-gray-50 rounded">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {user.nickname?.charAt(0) || user.email?.charAt(0) || 'U'}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{user.nickname || user.email}</p>
                  <p className="text-xs text-gray-500">新用户注册</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";
import React, { useState } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useAppContext } from "@/contexts/app";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

// mock积分消费记录数据（这部分暂时保留mock数据，因为需要后端API支持）
const records = [
  { time: "2024-06-01 10:23", used: 10, left: 120 },
  { time: "2024-06-02 14:11", used: 5, left: 115 },
  { time: "2024-06-03 09:45", used: 20, left: 95 },
  { time: "2024-06-04 12:30", used: 8, left: 87 },
  { time: "2024-06-05 16:20", used: 15, left: 72 },
  { time: "2024-06-06 09:10", used: 12, left: 60 },
  { time: "2024-06-07 18:05", used: 7, left: 53 },
];

export default function UserCenterPage() {
  const [tab, setTab] = useState<'log' | 'info'>("log");
  const { user } = useAppContext();
  const router = useRouter();

  // 检查用户是否已登录，如果未登录则重定向到登录页面
  useEffect(() => {
    if (!user) {
      router.push('/auth/signin?callbackUrl=' + encodeURIComponent('/i/user-center'));
    }
  }, [user, router]);

  // 如果用户未登录，显示加载状态
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-purple-50 flex flex-col items-center justify-center">
        <div className="text-lg">正在加载...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-purple-50 flex flex-col items-center py-16">
      {/* 如需调整白色卡片宽度，可修改max-w-7xl为max-w-6xl、max-w-4xl等，或直接用w-[1200px]自定义 */}
      <div className="w-full max-w-7xl bg-white rounded-xl shadow-2xl p-12 flex flex-col md:flex-row gap-12">
        {/* 左侧：头像+按钮 */}
        <div className="flex flex-col items-center md:items-start min-w-[200px]">
          <div className="flex items-center gap-6 mb-8">
            <Avatar className="w-20 h-20">
              <AvatarImage src={user.avatar_url} alt={user.nickname} />
              <AvatarFallback>{user.nickname?.charAt(0) || 'U'}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-gray-500 text-base">剩余积分</span>
              <span className="text-3xl font-bold text-green-600">{user.credits?.left_credits || 0}</span>
            </div>
          </div>
          <Button
            className={`w-40 mb-4 ${tab === "log" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => setTab("log")}
            variant="ghost"
            size="lg"
          >
            积分消费明细
          </Button>
          <Button
            className={`w-40 ${tab === "info" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => setTab("info")}
            variant="ghost"
            size="lg"
          >
            账户信息
          </Button>
        </div>
        {/* 右侧内容区 */}
        <div className="flex-1 min-w-0">
          {tab === "log" ? (
            <div>
              <div className="text-2xl font-bold mb-6">积分消费明细</div>
              <div className="h-96 overflow-y-auto pr-2 border rounded-2xl bg-blue-50/40 shadow-inner">
                {/* 表头 */}
                <div className="grid grid-cols-3 font-semibold text-blue-700 bg-blue-100 py-3 px-2 sticky top-0 z-10 rounded-t-2xl border-b border-blue-200">
                  <div className="text-center">消费时间</div>
                  <div className="text-center">消耗积分</div>
                  <div className="text-center">剩余积分</div>
                </div>
                <ul className="divide-y divide-blue-100">
                  {records.map((r, i) => (
                    <li key={i} className="grid grid-cols-3 items-center py-3 px-2">
                      <div className="text-gray-600 text-center text-base">{r.time}</div>
                      <div className="text-red-600 font-bold text-center text-base">- {r.used}</div>
                      <div className="text-green-700 text-center text-base">{r.left}</div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ) : (
            <div>
              <div className="text-2xl font-bold mb-6">账户信息</div>
              <div className="bg-gray-50 rounded-2xl p-8 flex flex-col gap-6 w-full max-w-lg shadow">
                <div className="flex gap-6 items-center">
                  <Avatar className="w-16 h-16">
                    <AvatarImage src={user.avatar_url} alt={user.nickname} />
                    <AvatarFallback>{user.nickname?.charAt(0) || 'U'}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-bold text-xl">{user.nickname}</div>
                    <div className="text-gray-500 text-base">昵称</div>
                  </div>
                </div>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="text-gray-700 text-base"><span className="font-semibold">ID：</span>{user.uuid}</div>
                  <div className="text-gray-700 text-base"><span className="font-semibold">邮箱：</span>{user.email}</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 
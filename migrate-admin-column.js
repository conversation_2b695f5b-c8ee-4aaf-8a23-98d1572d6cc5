const { Pool } = require('pg');

// 从环境变量读取数据库连接字符串
const DATABASE_URL = "postgresql://postgres.bounvfxusothhgzgpnyr:<EMAIL>:6543/postgres";

async function migrateAdminColumn() {
  const pool = new Pool({
    connectionString: DATABASE_URL,
  });

  try {
    console.log('连接数据库...');
    
    // 添加 is_admin 列
    const result = await pool.query(`
      ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN NOT NULL DEFAULT FALSE;
    `);
    
    console.log('✅ 成功添加 is_admin 列到 users 表');
    
    // 检查列是否存在
    const checkResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'is_admin';
    `);
    
    if (checkResult.rows.length > 0) {
      console.log('✅ 确认 is_admin 列已存在');
    } else {
      console.log('❌ is_admin 列未找到');
    }
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
  } finally {
    await pool.end();
    console.log('数据库连接已关闭');
  }
}

migrateAdminColumn();

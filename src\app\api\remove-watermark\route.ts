import { NextRequest, NextResponse } from "next/server";

const TEXTIN_API_URL = "https://api.textin.com/ai/service/v1/image/watermark_remove";
const APP_ID = "f0fee7f6142b26899544bf7a1486cb96";
const SECRET_CODE = "6ebf2a8b1e05bc74589ed39432bb4fe1";

export const runtime = "edge";

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const file = formData.get("file");
  if (!file || !(file instanceof Blob)) {
    return NextResponse.json({ error: "未上传图片文件" }, { status: 400 });
  }

  // 将图片转为二进制流
  const arrayBuffer = await file.arrayBuffer();

  const res = await fetch(TEXTIN_API_URL, {
    method: "POST",
    headers: {
      "x-ti-app-id": APP_ID,
      "x-ti-secret-code": SECRET_CODE,
      "Content-Type": "application/octet-stream",
    },
    body: Buffer.from(arrayBuffer),
  });

  const data = await res.json();
  if (data && data.code === 200 && data.result && data.result.image) {
    // 返回base64图片
    return NextResponse.json({ image: data.result.image });
  } else {
    return NextResponse.json({ error: data.message || "去水印失败", detail: data }, { status: 500 });
  }
} 
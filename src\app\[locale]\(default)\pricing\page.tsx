'use client';

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Star, Zap, Crown } from "lucide-react";
import { useAppContext } from "@/contexts/app";
import { toast } from "sonner";

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  credits: number;
  price: number;
  originalPrice?: number;
  currency: string;
  interval: 'month' | 'year' | 'one-time';
  features: string[];
  popular?: boolean;
  icon: React.ReactNode;
}

const monthlyPlans: PricingPlan[] = [
  {
    id: "basic-monthly",
    name: "基础版",
    description: "100积分/月",
    credits: 100,
    price: 7.13,
    currency: "USD",
    interval: "month",
    features: ["100积分/月", "$0.07/积分", "自动续费", "高质量去水印", "24/7客服支持"],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "standard-monthly",
    name: "标准版",
    description: "200积分/月",
    credits: 200,
    price: 10.19,
    currency: "USD",
    interval: "month",
    features: ["200积分/月", "$0.05/积分", "自动续费", "批量处理", "优先处理"],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-monthly",
    name: "专业版",
    description: "500积分/月",
    credits: 500,
    price: 15.30,
    currency: "USD",
    interval: "month",
    features: ["500积分/月", "$0.03/积分", "自动续费", "API访问", "高级客服"],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "enterprise-monthly",
    name: "企业版",
    description: "1000积分/月",
    credits: 1000,
    price: 20.40,
    currency: "USD",
    interval: "month",
    features: ["1000积分/月", "$0.02/积分", "自动续费", "团队管理", "专属客服"],
    icon: <Crown className="w-6 h-6" />,
  },
];

const yearlyPlans: PricingPlan[] = [
  {
    id: "basic-yearly",
    name: "基础版",
    description: "100积分/月 × 12",
    credits: 1200,
    price: 45.91,
    originalPrice: 85.56,
    currency: "USD",
    interval: "year",
    features: ["1200积分/年", "$0.04/积分", "一次性年付", "高质量去水印", "24/7客服支持"],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "standard-yearly",
    name: "标准版",
    description: "200积分/月 × 12",
    credits: 2400,
    price: 71.43,
    originalPrice: 122.28,
    currency: "USD",
    interval: "year",
    features: ["2400积分/年", "$0.03/积分", "一次性年付", "批量处理", "优先处理"],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-yearly",
    name: "专业版",
    description: "500积分/月 × 12",
    credits: 6000,
    price: 101.97,
    originalPrice: 183.60,
    currency: "USD",
    interval: "year",
    features: ["6000积分/年", "$0.02/积分", "一次性年付", "API访问", "高级客服"],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "enterprise-yearly",
    name: "企业版",
    description: "1000积分/月 × 12",
    credits: 12000,
    price: 142.86,
    originalPrice: 244.80,
    currency: "USD",
    interval: "year",
    features: ["12000积分/年", "$0.01/积分", "一次性年付", "团队管理", "专属客服"],
    icon: <Crown className="w-6 h-6" />,
  },
];

const oneTimePlans: PricingPlan[] = [
  {
    id: "starter-pack",
    name: "入门包",
    description: "50积分买断",
    credits: 50,
    price: 8.15,
    currency: "USD",
    interval: "one-time",
    features: ["50积分", "$0.16/积分", "永不过期", "不自动续费", "高质量去水印"],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "value-pack",
    name: "超值包",
    description: "200积分买断",
    credits: 200,
    price: 25.50,
    currency: "USD",
    interval: "one-time",
    features: ["200积分", "$0.13/积分", "永不过期", "不自动续费", "批量处理"],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-pack",
    name: "专业包",
    description: "500积分买断",
    credits: 500,
    price: 56.12,
    currency: "USD",
    interval: "one-time",
    features: ["500积分", "$0.11/积分", "永不过期", "不自动续费", "优先处理"],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "mega-pack",
    name: "超级包",
    description: "1000积分买断",
    credits: 1000,
    price: 86.73,
    currency: "USD",
    interval: "one-time",
    features: ["1000积分", "$0.09/积分", "永不过期", "不自动续费", "API访问"],
    icon: <Crown className="w-6 h-6" />,
  },
];

const PricingPage = () => {
  const [planType, setPlanType] = useState<'monthly' | 'yearly' | 'one-time'>('yearly');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null);
  const { user, setShowSignModal } = useAppContext();

  const handleSubscribe = async (plan: PricingPlan) => {
    if (!user) {
      setShowSignModal(true);
      return;
    }

    setIsLoading(true);
    setLoadingPlanId(plan.id);

    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          product_id: plan.id,
          product_name: plan.name,
          credits: plan.credits,
          interval: plan.interval,
          amount: Math.round(plan.price * 100), // 转换为分
          currency: plan.currency.toLowerCase(),
          valid_months: plan.interval === 'year' ? 12 : plan.interval === 'month' ? 1 : 0,
        }),
      });

      if (response.status === 401) {
        setShowSignModal(true);
        return;
      }

      if (!response.ok) {
        throw new Error('支付请求失败');
      }

      const data = await response.json();
      if (data.code === 0) {
        // 这里可以集成Stripe或PayPal支付
        toast.success('正在跳转到支付页面...');
        // 实际项目中这里会跳转到支付页面
        console.log('Payment data:', data);
      } else {
        throw new Error(data.message || '支付请求失败');
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('支付请求失败，请稍后重试');
    } finally {
      setIsLoading(false);
      setLoadingPlanId(null);
    }
  };

  const getCurrentPlans = () => {
    switch (planType) {
      case 'monthly':
        return monthlyPlans;
      case 'yearly':
        return yearlyPlans;
      case 'one-time':
        return oneTimePlans;
      default:
        return yearlyPlans;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="w-full py-6 px-4 border-b border-white/10 backdrop-blur-sm bg-black/20">
        <div className="max-w-7xl mx-auto flex justify-center items-center">
          <img src="/logo.png" alt="Watermark Remover" className="h-10 mr-3" />
          <span className="text-2xl font-bold tracking-tight text-white">定价与订阅</span>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-16">
        {/* Title Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold mb-6 text-white">
            选择适合您的
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> 定价计划</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            专业的AI去水印服务，为您提供高质量的图片处理体验。选择最适合您需求的计划，立即开始使用。
          </p>

          {/* Free Plan Info */}
          <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-6 border border-green-500/30 max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-gradient-to-r from-green-500 to-blue-500 p-3 rounded-2xl">
                <Zap className="w-6 h-6 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">🎉 免费计划</h3>
            <p className="text-gray-300 mb-4">
              每月免费3次去水印机会，每日限制1次使用
            </p>
            <div className="text-sm text-gray-400">
              注册即可享受 • 无需信用卡 • 体验完整功能
            </div>
          </div>
        </div>

        {/* Plan Type Selector */}
        <div className="flex justify-center mb-12">
          <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-2 border border-white/10">
            <div className="flex space-x-2">
              {[
                { key: 'monthly', label: '💳 按月自动续费', badge: null },
                { key: 'yearly', label: '🗓️ 按年一次性付费', badge: '最划算' },
                { key: 'one-time', label: '💰 一次性购买', badge: '买断' },
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setPlanType(option.key as any)}
                  className={`relative px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${
                    planType === option.key
                      ? 'bg-white text-gray-900 shadow-lg'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  {option.label}
                  {option.badge && (
                    <Badge className="absolute -top-2 -right-2 bg-gradient-to-r from-green-400 to-blue-500 text-white text-xs">
                      {option.badge}
                    </Badge>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {getCurrentPlans().map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-black/40 backdrop-blur-sm rounded-3xl p-8 border transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                plan.popular
                  ? 'border-purple-500 shadow-purple-500/20 shadow-2xl'
                  : 'border-white/10 hover:border-white/20'
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 text-sm font-semibold">
                    最受欢迎
                  </Badge>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <div className="flex justify-center mb-4">
                  <div className={`p-3 rounded-2xl ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500'
                      : 'bg-gradient-to-r from-blue-500 to-cyan-500'
                  }`}>
                    {plan.icon}
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-4">{plan.description}</p>

                {/* Price */}
                <div className="mb-4">
                  {plan.originalPrice && (
                    <div className="text-gray-500 line-through text-lg mb-1">
                      ${plan.originalPrice}
                    </div>
                  )}
                  <div className="text-4xl font-bold text-white mb-1">
                    ${plan.price}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {plan.interval === 'month' && '/ 月'}
                    {plan.interval === 'year' && '/ 年'}
                    {plan.interval === 'one-time' && '一次性'}
                  </div>
                </div>

                {/* Credits */}
                <div className="bg-white/5 rounded-xl p-3 mb-6">
                  <div className="text-2xl font-bold text-white">{plan.credits}</div>
                  <div className="text-gray-400 text-sm">积分</div>
                </div>
              </div>

              {/* Features */}
              <div className="mb-8">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-300">
                      <Check className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Subscribe Button */}
              <Button
                onClick={() => handleSubscribe(plan)}
                disabled={isLoading}
                className={`w-full py-3 text-lg font-semibold rounded-xl transition-all duration-200 ${
                  plan.popular
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white'
                    : 'bg-white text-gray-900 hover:bg-gray-100'
                } ${isLoading && loadingPlanId === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isLoading && loadingPlanId === plan.id ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current mr-2"></div>
                    处理中...
                  </div>
                ) : (
                  '立即订阅'
                )}
              </Button>
            </div>
          ))}
        </div>

        {/* Payment Methods */}
        <div className="text-center">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-white mb-4">支持的支付方式</h3>
            <div className="flex items-center justify-center gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                <img src="/imgs/logos/paypal.png" alt="PayPal" className="h-8" />
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                <div className="text-white font-semibold">Stripe</div>
              </div>
            </div>
          </div>
          <p className="text-gray-400 text-sm">
            安全支付 • 30天退款保证 • 24/7客服支持
          </p>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-white/10 bg-black/20 backdrop-blur-sm py-8">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} Watermark Remover. Powered by Next.js.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default PricingPage;

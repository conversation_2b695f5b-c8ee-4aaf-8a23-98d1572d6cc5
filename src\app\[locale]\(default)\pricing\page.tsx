'use client';

import React, { useState } from "react";

const monthlyPlans = [
  { quota: "100积分/月", price: "$0.07", total: "$7.13" },
  { quota: "200积分/月", price: "$0.05", total: "$10.19" },
  { quota: "500积分/月", price: "$0.03", total: "$15.30" },
  { quota: "1000积分/月", price: "$0.02", total: "$20.40" },
];
const yearlyPlans = [
  { quota: "100积分/月 ×12", price: "$0.04", total: "$45.91" },
  { quota: "200积分/月 ×12", price: "$0.03", total: "$71.43" },
  { quota: "500积分/月 ×12", price: "$0.02", total: "$101.97" },
  { quota: "1000积分/月 ×12", price: "$0.01", total: "$142.86" },
];
const oneTimePlans = [
  { quota: "100积分/月 ×12", price: "$0.04", total: "$45.91" },
  { quota: "200积分/月 ×12", price: "$0.03", total: "$71.43" },
  { quota: "500积分/月 ×12", price: "$0.02", total: "$101.97" },
  { quota: "1000积分/月 ×12", price: "$0.01", total: "$142.86" },
];

const PricingPage = () => {
  const [subTab, setSubTab] = useState<'monthly' | 'yearly'>("yearly");
  return (
    <div className="min-h-screen bg-neutral-900 flex flex-col items-center">
      <header className="w-full py-6 flex justify-center items-center border-b border-neutral-800 bg-neutral-900">
        <img src="/logo.png" alt="Watermark Remover" className="h-10 mr-3" />
        <span className="text-2xl font-bold tracking-tight text-white">定价与订阅</span>
      </header>
      <main className="flex-1 w-full max-w-6xl flex flex-col items-center justify-center px-4 py-10">
        <h1 className="text-3xl font-bold mb-4 text-center text-white">为您选择合适的计划</h1>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 w-full mt-8">
          {/* 免费计划 */}
          <div className="bg-neutral-800 rounded-2xl shadow p-8 flex flex-col items-center border border-neutral-700">
            <div className="text-lg font-bold mb-2 text-white">免费计划</div>
            <div className="text-3xl font-bold text-yellow-400 mb-2">$0</div>
            <button className="w-full py-2 px-4 bg-yellow-500 text-white rounded font-semibold mb-4">免费体验</button>
            <ul className="text-neutral-300 text-sm mb-4 list-disc list-inside w-full">
              <li>每月3次免费额度</li>
              <li>每日最多1次</li>
              <li>支持主流图片格式</li>
              <li>AI智能处理</li>
            </ul>
          </div>
          {/* 订阅计划 */}
          <div className="bg-neutral-800 rounded-2xl shadow p-8 flex flex-col items-center border-2 border-red-400 relative">
            <div className="absolute -top-5 left-1/2 -translate-x-1/2 bg-red-500 text-white text-xs px-4 py-1 rounded-full font-bold">最受欢迎</div>
            <div className="text-lg font-bold mb-2 text-white">订阅计划</div>
            <div className="flex gap-2 mb-4">
              <button onClick={()=>setSubTab('monthly')} className={`px-4 py-1 rounded-full text-sm font-semibold ${subTab==='monthly'?'bg-red-500 text-white':'bg-neutral-700 text-neutral-300'}`}>每月</button>
              <button onClick={()=>setSubTab('yearly')} className={`px-4 py-1 rounded-full text-sm font-semibold ${subTab==='yearly'?'bg-green-500 text-white':'bg-neutral-700 text-neutral-300'}`}>每年（省45%）</button>
            </div>
            <div className="w-full overflow-x-auto">
              <table className="w-full text-center text-neutral-200 mb-4">
                <thead>
                  <tr className="bg-neutral-700">
                    <th className="py-2">配额</th>
                    <th className="py-2">单价(USD/积分)</th>
                    <th className="py-2">{subTab==='monthly'?'月费用(USD)':'年费用(USD)'}</th>
                  </tr>
                </thead>
                <tbody>
                  {(subTab==='monthly'?monthlyPlans:yearlyPlans).map((p,i)=>(
                    <tr key={i} className="border-b border-neutral-700">
                      <td className="py-2">{p.quota}</td>
                      <td className="py-2">{p.price}</td>
                      <td className="py-2">{p.total}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <button className="w-full py-2 px-4 bg-red-500 text-white rounded font-semibold hover:bg-red-600 transition">立即订阅</button>
          </div>
          {/* 一次性购买 */}
          <div className="bg-neutral-800 rounded-2xl shadow p-8 flex flex-col items-center border border-neutral-700">
            <div className="text-lg font-bold mb-2 text-white">一次性购买</div>
            <div className="text-3xl font-bold text-yellow-400 mb-2">买断积分</div>
            <button className="w-full py-2 px-4 bg-yellow-500 text-white rounded font-semibold mb-4">立即购买</button>
            <div className="w-full overflow-x-auto">
              <table className="w-full text-center text-neutral-200 mb-4">
                <thead>
                  <tr className="bg-neutral-700">
                    <th className="py-2">配额</th>
                    <th className="py-2">单价(USD/积分)</th>
                    <th className="py-2">年费用(USD)</th>
                  </tr>
                </thead>
                <tbody>
                  {oneTimePlans.map((p,i)=>(
                    <tr key={i} className="border-b border-neutral-700">
                      <td className="py-2">{p.quota}</td>
                      <td className="py-2">{p.price}</td>
                      <td className="py-2">{p.total}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div className="mt-10 text-center text-neutral-400 text-sm">
          <div className="mb-2">支持支付方式：</div>
          <div className="flex items-center justify-center gap-4">
            <img src="/imgs/logos/paypal.png" alt="PayPal" className="h-8" />
          </div>
        </div>
      </main>
      <footer className="w-full py-6 text-center text-neutral-500 text-xs border-t border-neutral-800 bg-neutral-900 mt-10">
        &copy; {new Date().getFullYear()} Watermark Remover. Powered by Next.js.
      </footer>
    </div>
  );
};

export default PricingPage;
